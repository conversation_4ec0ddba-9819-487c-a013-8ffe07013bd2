# ATMA Backend Universal Startup Script for Windows PowerShell
# Cleaned & Optimized version

param(
    [switch]$Help,
    [switch]$Version,
    [switch]$Quick,
    [switch]$Clean,
    [switch]$NoPull,
    [switch]$NoCloudflare,
    [switch]$NoDocs,
    [int]$Workers = 10,
    [int]$Wait = 15,
    [switch]$Simple,
    [switch]$Staged,
    [switch]$Status,
    [switch]$Stop,
    [switch]$Restart
)

$SCRIPT_VERSION = "1.0.1"
$SCRIPT_NAME = "ATMA Universal Startup"

function Write-Header($Message) {
    Write-Host "`n========================================"
    Write-Host "    🚀 $Message"
    Write-Host "========================================`n"
}

function Write-Status($Message) { Write-Host "[INFO] $Message" -ForegroundColor Blue }
function Write-Success($Message) { Write-Host "[SUCCESS] $Message" -ForegroundColor Green }
function Write-Warning($Message) { Write-Host "[WARNING] $Message" -ForegroundColor Yellow }
function Write-ErrorMsg($Message) { Write-Host "[ERROR] $Message" -ForegroundColor Red }
function Write-Step($Message) { Write-Host "[STEP] $Message" -ForegroundColor Cyan }

function Show-Help {
    Write-Host "$SCRIPT_NAME v$SCRIPT_VERSION" -ForegroundColor Green
    Write-Host "Usage: .\start-atma.ps1 [OPTIONS]"
    Write-Host "  -Help, -Version, -Quick, -Clean, -NoPull, -NoCloudflare, -NoDocs"
    Write-Host "  -Workers N (default 10), -Wait N (default 15), -Simple, -Staged"
    Write-Host "  -Status, -Stop, -Restart"
}

function Test-Docker {
    if (!(Get-Command docker -ErrorAction SilentlyContinue)) { Write-ErrorMsg "Docker not found"; exit 1 }
    docker info >$null 2>&1; if ($LASTEXITCODE -ne 0) { Write-ErrorMsg "Docker not running"; exit 1 }
    if (!(Get-Command docker-compose -ErrorAction SilentlyContinue)) { Write-ErrorMsg "Docker Compose not found"; exit 1 }
}

function Wait-ForService($ServiceName, $MaxAttempts = 30) {
    Write-Status "Waiting for $ServiceName to be healthy..."
    for ($i = 1; $i -le $MaxAttempts; $i++) {
        $status = docker-compose ps $ServiceName 2>$null
        if ($status -match "healthy") { Write-Success "$ServiceName is healthy!"; return }
        elseif ($status -match "unhealthy") { Write-Warning "$ServiceName unhealthy ($i/$MaxAttempts)" }
        else { Write-Status "$ServiceName starting ($i/$MaxAttempts)" }
        Start-Sleep -Seconds 2
    }
    Write-ErrorMsg "$ServiceName failed health check"
}

function Start-AndWait($Services, $SkipHealthCheck = $false) {
    Write-Step "Starting: $($Services -join ", ")"
    docker-compose up --build -d $Services
    if ($LASTEXITCODE -ne 0) { Write-ErrorMsg "Failed to start $Services"; return $false }
    if (-not $SkipHealthCheck) { foreach ($s in $Services) { Wait-ForService $s } }
    return $true
}

function Show-Status {
    Write-Header "Service Status"
    docker-compose ps
    Write-Status "Service URLs:"
    Write-Host "🌐 API Gateway: http://localhost:3000"
    Write-Host "🔐 Auth Service: http://localhost:3001"
    Write-Host "📁 Archive Service: http://localhost:3002"
    Write-Host "📝 Assessment Service: http://localhost:3003"
    Write-Host "🔔 Notification Service: http://localhost:3005"
    Write-Host "📚 Documentation: http://localhost:8080"
    Write-Host "🐰 RabbitMQ: http://localhost:15672 (admin/admin123)"
}

function Stop-Services {
    Write-Header "Stopping Services"
    docker-compose down --volumes --remove-orphans
    Write-Success "All services stopped"
}

function Clean-Docker {
    Write-Status "Cleaning Docker system..."
    docker-compose down --volumes --remove-orphans >$null 2>&1
    docker system prune -a --volumes -f
    Write-Success "Docker system cleaned"
}

function Start-Simple($WorkerCount, $SkipPull, $SkipCloudflare, $SkipDocs) {
    Write-Header "Simple Start Mode"
    if (-not $SkipPull) { Write-Status "Pulling latest images..."; docker-compose pull }
    $services = @("postgres", "redis", "rabbitmq", "auth-service", "assessment-service", "archive-service", "notification-service", "api-gateway", "testing-service")
    for ($i = 1; $i -le $WorkerCount; $i++) { $services += "analysis-worker-$i" }
    if (-not $SkipDocs) { $services += "documentation-service" }
    if (-not $SkipCloudflare) { $services += "cloudflared" }
    docker-compose up --build -d $services
    Start-Sleep -Seconds $Wait
    Show-Status
    Write-Success "Backend started (simple mode)"
}

function Start-Staged($WorkerCount, $SkipPull, $SkipCloudflare, $SkipDocs) {
    Write-Header "Staged Start Mode"
    if (-not $SkipPull) { Write-Status "Pulling latest images..."; docker-compose pull }
    Start-AndWait @("postgres", "redis", "rabbitmq")
    Start-AndWait @("auth-service")
    Start-AndWait @("assessment-service", "archive-service", "notification-service")
    Start-AndWait @("api-gateway")
    $workers = @(for ($i = 1; $i -le $WorkerCount; $i++) { "analysis-worker-$i" })
    docker-compose up --build -d $workers; foreach ($w in $workers) { Wait-ForService $w 10 }
    Start-AndWait @("testing-service")
    if (-not $SkipDocs) { Start-AndWait @("documentation-service") }
    if (-not $SkipCloudflare) {
        if (Test-Path ".env") {
            $envContent = Get-Content ".env"
            $tokenLine = $envContent | Where-Object { $_ -match "CLOUDFLARE_TUNNEL_TOKEN=" }
            if ($tokenLine) {
                $token = ($tokenLine -split "=", 2)[1]
                if ($token -and $token -ne "your_token_here") {
                    Write-Status "Starting Cloudflare tunnel..."
                    docker-compose up -d cloudflared
                } else { Write-Warning "Cloudflare token not set, skipping" }
            } else { Write-Warning "Cloudflare token missing, skipping" }
        } else { Write-Warning ".env not found, skipping Cloudflare" }
    }
    Show-Status
    Write-Success "Backend started (staged mode)"
}

function Main {
    if ($Help) { Show-Help; return }
    if ($Version) { Write-Host "$SCRIPT_NAME v$SCRIPT_VERSION"; return }
    Test-Docker
    if ($Status) { Show-Status; return }
    if ($Stop) { Stop-Services; return }
    if ($Restart) { Stop-Services; Start-Sleep -Seconds 2 }
    if ($Quick) { $NoPull = $true; $Clean = $false }
    if (-not $Simple -and -not $Staged) { $Staged = $true }
    if ($Clean) { Clean-Docker }
    try {
        if ($Simple) { Start-Simple $Workers $NoPull $NoCloudflare $NoDocs }
        else { Start-Staged $Workers $NoPull $NoCloudflare $NoDocs }
        Write-Status "To run tests:`n   .\run-tests.ps1`n   node testing/e2e-test.js`n   node testing/load-test.js"
    } catch {
        Write-ErrorMsg "Startup failed: $_"
        docker-compose ps
        exit 1
    }
}

Main
